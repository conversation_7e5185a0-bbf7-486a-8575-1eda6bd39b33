import type React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>_Display, <PERSON><PERSON> } from "next/font/google"
import type { <PERSON>ada<PERSON> } from "next"
import { Toaster } from "@/components/ui/toaster"
import { ThemeProvider } from "@/components/theme-provider"
import { LanguageProvider } from "@/components/language-provider"
import Header from "@/components/header"
import Footer from "@/components/footer"
import PageTransition from "@/components/page-transition"
import LoadingBar from "@/components/loading-bar"
import TranslationSafeWrapper from "@/components/translation-safe-wrapper"
import TranslationDebug from "@/components/translation-debug"
import "./globals.css"

const inter = Inter({ subsets: ["latin"], variable: "--font-inter" })
const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
  variable: "--font-poppins",
})
const amiri = <PERSON><PERSON>({
  weight: ["400", "700"],
  subsets: ["arabic"],
  variable: "--font-amiri",
})

const playfair = Playfair_Display({
  weight: ["400", "500", "600", "700", "800", "900"],
  subsets: ["latin"],
  variable: "--font-playfair",
})

const sora = Sora({
  weight: ["100", "200", "300", "400", "500", "600", "700", "800"],
  subsets: ["latin"],
  variable: "--font-sora",
})

const SITE_ENABLED = false;

export const metadata: Metadata = {
  title: {
    default: "Isnad Foundation",
    template: "%s | Isnad Foundation",
  },
  description:
    "Providing scholarships, university admissions, and academic support for Palestinian students across the world.",
  keywords: ["scholarships", "education", "palestinian students", "academic support", "university admissions"],
  generator: 'v0.dev',
  icons: {
    icon: '/logo.png',
    apple: [
      { url: '/apple-touch-icon.png' },
      { url: '/apple-touch-icon-precomposed.png' }
    ]
  }
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} ${poppins.variable} ${amiri.variable} ${playfair.variable} ${sora.variable} font-sora`}>
        <TranslationSafeWrapper>
          <ThemeProvider attribute="class" defaultTheme="light" enableSystem disableTransitionOnChange>
            <LanguageProvider>
              <LoadingBar />
              <div className="flex min-h-screen flex-col">
                <Header />
                <main className="flex-1">
                  <PageTransition>{children}</PageTransition>
                </main>
                <Footer />
              </div>
              <Toaster />
            </LanguageProvider>
          </ThemeProvider>
          <TranslationDebug />
        </TranslationSafeWrapper>
      </body>
    </html>
  )
}



import './globals.css'
