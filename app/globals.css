@tailwind base;
@tailwind components;
@tailwind utilities;

/* Smooth page transitions */
html {
  scroll-behavior: smooth;
}

/* Prevent layout shift during transitions */
body {
  overflow-x: hidden;
}

/* Smooth scrolling for all elements */
* {
  scroll-behavior: smooth;
}

@layer base {
  :root {
    /* Palestinian flag colors */
    --red: 0 76% 40%;
    --green: 120 61% 34%;
    --black: 0 0% 0%;
    --white: 0 0% 100%;

    /* Light mode */
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 0%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 0%;
    --primary: 120 61% 34%; /* Green */
    --primary-foreground: 0 0% 100%;
    --secondary: 0 76% 40%; /* Red */
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;
    --accent: 0 0% 0%; /* Black */
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 120 61% 34%;
    --radius: 0.5rem;
  }

  .dark {
    /* Dark mode */
    --background: 0 0% 10%;
    --foreground: 0 0% 98%;
    --card: 0 0% 15%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 15%;
    --popover-foreground: 0 0% 98%;
    --primary: 120 61% 34%; /* Green */
    --primary-foreground: 0 0% 100%;
    --secondary: 0 76% 40%; /* Red */
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 20%;
    --muted-foreground: 0 0% 70%;
    --accent: 0 0% 0%; /* Black */
    --accent-foreground: 0 0% 100%;
    --destructive: 0 62% 30%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 120 61% 34%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sora;
  }
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-sora font-bold;
  }
  /* Arabic text styling */
  :lang(ar) {
    @apply font-amiri;
    letter-spacing: -0.5px; /* Reduce letter spacing for Arabic */
    line-height: 1.6;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1; /* Enable ligatures */
    word-spacing: -0.15em; /* Reduce space between words */
  }

  :lang(ar) h1 {
    @apply leading-[1.6];
    text-align: right; /* Ensure proper RTL alignment */
    word-spacing: -0.25em; /* Further reduce space between words for headlines */
    letter-spacing: -1px; /* Tighter letter spacing for headlines */
  }
}

/* GSAP Animations */
.reveal-text {
  clip-path: polygon(0 0, 100% 0, 100% 0, 0 0);
}

.reveal-text.revealed {
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
}

.scale-up {
  transform: scale(0.8);
  opacity: 0;
}

.scale-up.revealed {
  transform: scale(1);
  opacity: 1;
}

.slide-up {
  transform: translateY(50px);
  opacity: 0;
}

.slide-up.revealed {
  transform: translateY(0);
  opacity: 1;
}

.slide-right {
  transform: translateX(-50px);
  opacity: 0;
}

.slide-right.revealed {
  transform: translateX(0);
  opacity: 1;
}

.slide-left {
  transform: translateX(50px);
  opacity: 0;
}

.slide-left.revealed {
  transform: translateX(0);
  opacity: 1;
}

/* RTL Support */
[dir="rtl"] .slide-right {
  transform: translateX(50px);
  opacity: 0;
}

[dir="rtl"] .slide-right.revealed {
  transform: translateX(0);
  opacity: 1;
}

[dir="rtl"] .slide-left {
  transform: translateX(-50px);
  opacity: 0;
}

[dir="rtl"] .slide-left.revealed {
  transform: translateX(0);
  opacity: 1;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--background));
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--primary) / 0.5);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.8);
}

/* Progress bar */
.progress-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: hsl(var(--primary));
  transform-origin: 0%;
  z-index: 100;
}

/* Custom Animations */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Language-specific fonts */
html[lang="en"] {
  font-family: var(--font-sora), sans-serif;
}

html[lang="ar"] {
  font-family: var(--font-amiri), serif;
}

html[lang="tr"] {
  font-family: var(--font-sora), sans-serif;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion) {
  html {
    scroll-behavior: auto;
  }
}

/* Specific styling for hero title in Arabic - make it more specific */
h1.hero-title:lang(ar) {
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1, "rlig" 1;
  text-align: center;
  word-spacing: -0.1em !important; /* Increased space between words and forced with !important */
  letter-spacing: -2px;
}

/* You can remove or comment out these general styles since we're targeting the hero title specifically */
/*.text-4xl:lang(ar),
.text-5xl:lang(ar),
.text-6xl:lang(ar),
.text-7xl:lang(ar) {
  line-height: 1.6;
  letter-spacing: -1.5px;
  word-spacing: -0.05em;
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}*/

/* Translation Support */
.translated {
  /* Ensure smooth transitions for translated content */
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

/* Prevent Google Translate from breaking animations */
[data-gsap-text-reveal="true"],
[data-scrolling-cards="true"] {
  /* Maintain transform during translation */
  transform-style: preserve-3d;
  backface-visibility: hidden;
  /* Prevent Google Translate from modifying these elements directly */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* Enhanced Google Translate compatibility */
.translation-safe-content {
  /* Ensure proper isolation for translated content */
  isolation: isolate;
  /* Prevent layout shifts during translation */
  contain: layout style;
}

/* Prevent Google Translate from breaking React hydration */
.translation-safe-content * {
  /* Ensure consistent text rendering */
  text-rendering: optimizeLegibility;
  /* Prevent font substitution issues */
  font-synthesis: none;
}

/* Handle Google Translate font wrappers gracefully */
font[style*="vertical-align: inherit"] {
  /* Maintain original styling */
  font-family: inherit !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  line-height: inherit !important;
  color: inherit !important;
  /* Prevent layout shifts */
  vertical-align: baseline !important;
}

/* Ensure proper RTL support for translated content */
[dir="rtl"] [data-gsap-text-reveal="true"],
[dir="rtl"] [data-scrolling-cards="true"] {
  /* Maintain RTL animations during translation */
  transform-origin: right center;
}

/* Improve animation performance */
.animate-on-translate {
  will-change: transform, opacity;
  /* Prevent layout shifts during translation */
  contain: layout style paint;
}

/* Handle Google Translate wrapper elements */
.goog-te-spinner-pos {
  /* Prevent spinner from breaking layout */
  position: fixed !important;
  z-index: 9999 !important;
}

.goog-te-banner-frame {
  /* Ensure translate banner doesn't break layout */
  display: none !important;
}

/* Ensure proper stacking context for translated elements */
.translated-element {
  isolation: isolate;
  z-index: 1;
}

/* Improve text rendering during translation */
.translated-text {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Additional Google Translate safety measures */
.goog-te-combo {
  /* Ensure translate widget doesn't break layout */
  position: relative !important;
  z-index: 1000 !important;
}

/* Hide Google Translate elements that can cause issues */
.goog-te-banner-frame.skiptranslate {
  display: none !important;
}

.goog-te-gadget-icon {
  /* Prevent icon from causing layout shifts */
  display: none !important;
}

/* Ensure translated content maintains proper spacing */
body.translated-ltr,
body.translated-rtl {
  /* Prevent body modifications from breaking layout */
  margin: 0 !important;
  padding: 0 !important;
}

/* Handle Google Translate's text highlighting */
.goog-text-highlight {
  /* Maintain original background and text colors */
  background-color: transparent !important;
  color: inherit !important;
  /* Prevent box-shadow from affecting layout */
  box-shadow: none !important;
}

/* Ensure proper handling of translated links */
a font[style*="vertical-align"] {
  /* Maintain link styling */
  color: inherit !important;
  text-decoration: inherit !important;
}

/* Prevent Google Translate from affecting form elements */
input font[style*="vertical-align"],
textarea font[style*="vertical-align"],
select font[style*="vertical-align"] {
  /* Maintain form element styling */
  font-family: inherit !important;
  font-size: inherit !important;
}

/* Additional DOM safety measures for Google Translate - Only when translation is active */
body.translated-ltr .translation-safe-content,
body.translated-rtl .translation-safe-content {
  /* Prevent React from losing track of nodes only when translated */
  contain: layout style;
  /* Ensure proper isolation only when needed */
  isolation: isolate;
}

/* Handle Google Translate's font wrapper elements more aggressively */
font[style*="vertical-align: inherit"] * {
  /* Prevent nested font elements from breaking DOM structure */
  font-family: inherit !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  color: inherit !important;
  line-height: inherit !important;
  vertical-align: inherit !important;
}

/* Only apply aggressive fixes when Google Translate is actually active */
body.translated-ltr [data-reactroot] font[style*="vertical-align"],
body.translated-rtl [data-reactroot] font[style*="vertical-align"],
body.translated-ltr [data-react-helmet] font[style*="vertical-align"],
body.translated-rtl [data-react-helmet] font[style*="vertical-align"] {
  /* Maintain React component integrity only when translated */
  display: contents !important;
}

/* Prevent Google Translate from affecting critical layout elements only when active */
body.translated-ltr header font[style*="vertical-align"],
body.translated-rtl header font[style*="vertical-align"],
body.translated-ltr nav font[style*="vertical-align"],
body.translated-rtl nav font[style*="vertical-align"],
body.translated-ltr main font[style*="vertical-align"],
body.translated-rtl main font[style*="vertical-align"],
body.translated-ltr footer font[style*="vertical-align"],
body.translated-rtl footer font[style*="vertical-align"] {
  /* Maintain semantic structure only when translated */
  display: contents !important;
}



